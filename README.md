# 🚀 Super Stock Agents

> 基于多代理系统的智能股票题材分析平台

[![Python](https://img.shields.io/badge/Python-3.8+-blue.svg)](https://python.org)
[![Flask](https://img.shields.io/badge/Flask-2.3+-green.svg)](https://flask.palletsprojects.com)
[![AgentScope](https://img.shields.io/badge/AgentScope-0.0.5+-orange.svg)](https://github.com/modelscope/agentscope)
[![License](https://img.shields.io/badge/License-Educational-red.svg)](#)

一个革命性的股票分析平台，通过5个专业AI代理的协同工作，为用户提供深度的股票题材分析和投资洞察。从传统的Gradio界面升级到现代化的Web应用，提供更优秀的用户体验和更强大的功能。

## ✨ 核心特性

### 🎯 智能多代理系统
- **5个专业代理协同工作**：每个代理都有独特的专业领域和分析角度
- **动态任务分配**：CEO代理智能分析用户需求并分配任务
- **实时协作**：代理间通过@机制进行实时沟通和信息传递

### 🌐 现代化Web界面
- **响应式设计**：完美适配桌面端、平板和移动设备
- **实时通信**：基于WebSocket的即时消息传输
- **优雅的UI/UX**：现代化设计语言，流畅的动画效果
- **会话管理**：支持多会话历史记录和搜索功能

### 🔧 技术优势
- **高性能架构**：Flask + WebSocket + 多进程处理
- **智能消息处理**：支持Markdown渲染、工具调用结果展示
- **调试模式**：内置测试模式，便于开发和调试
- **扩展性强**：模块化设计，易于添加新功能

## 👥 专业代理团队

我们的AI专家团队由5个高度专业化的代理组成，每个代理都有独特的技能和职责：

### 👔 Morgan (CEO & 战略规划师)
- **职责**：总体规划、任务分配、流程协调
- **技能**：需求分析、任务拆解、团队管理
- **模型**：DeepSeek-V3 (规划师配置)

### 🔍 Leo (信息检索专家)
- **职责**：网络信息检索、数据筛选、信息验证
- **技能**：实时数据获取、信息过滤、趋势识别
- **工具**：股票RAG工具、网络搜索、数据分析

### 📊 Ken (题材挖掘专家)
- **职责**：题材逻辑分析、分支挖掘、潜力评估
- **技能**：行业分析、概念挖掘、逻辑推演
- **模型**：DeepSeek-R1 (思考者配置)

### 💹 Lus (A股炒作大师)
- **职责**：个股挖掘、炒作分析、投资机会识别
- **技能**：技术分析、资金流向、市场情绪
- **专长**：A股市场特色、热点轮动、短线机会

### 📝 Jess (执行秘书)
- **职责**：信息汇总、报告生成、结果整理
- **技能**：文档编写、数据整合、格式化输出
- **模型**：Kimi-K2 (工具使用者配置)

## 🚀 快速开始

### 📋 系统要求

| 组件 | 最低版本 | 推荐版本 | 说明 |
|------|----------|----------|------|
| Python | 3.8+ | 3.11+ | 核心运行环境 |
| 内存 | 4GB | 8GB+ | 多代理并发处理 |
| 浏览器 | Chrome 90+ | 最新版本 | 支持WebSocket和ES6+ |
| 网络 | 稳定连接 | 高速网络 | API调用和实时数据获取 |

### ⚡ 一键安装

```bash
# 1. 克隆项目
git clone <repository-url>
cd stock_agent_html

# 2. 创建虚拟环境 (推荐)
python -m venv venv
source venv/bin/activate  # Linux/Mac
# 或
venv\Scripts\activate     # Windows

# 3. 安装依赖
pip install -r requirements.txt

# 4. 配置环境变量
cp .env.example .env      # 复制配置模板
# 编辑 .env 文件，填入您的API配置

# 5. 启动系统
python run.py
```

### 🔧 环境配置

创建 `.env` 文件并配置以下参数：

```env
# API配置 (必需)
API_URL=https://api.deepseek.com/v1          # API服务地址
API_KEY=your_deepseek_api_key_here           # API密钥

# 系统配置 (可选)
FLASK_ENV=production                         # 运行环境
SECRET_KEY=your_secret_key_here              # Flask密钥
DEBUG_MODE=false                             # 调试模式

# 代理配置 (可选)
MAX_MEMORY_LENGTH=50                         # 代理记忆长度
TIMEOUT_SECONDS=300                          # 请求超时时间
```

### 🎯 启动方式

#### 方式一：标准启动
```bash
python run.py
```

#### 方式二：Windows快速启动
```bash
# 双击运行
start_debug.bat
```

#### 方式三：开发模式
```bash
# 启用调试模式
python app.py
```

### 🌐 访问系统

启动成功后，在浏览器中访问：
- **本地访问**：http://localhost:5000
- **局域网访问**：http://your-ip:5000

首次访问时，系统会自动初始化代理团队，请耐心等待约10-30秒。

## 📖 使用指南

### 🎮 基础操作

#### 1️⃣ 系统初始化
- 首次访问时，系统会自动初始化代理团队
- 等待初始化完成（约10-30秒）
- 看到"系统已就绪"提示后即可开始使用

#### 2️⃣ 发起咨询
```
在输入框中描述您的分析需求，例如：
• "请分析人工智能题材的投资机会"
• "新能源汽车板块最新动态如何？"
• "半导体行业有哪些值得关注的个股？"
```

#### 3️⃣ 观察分析过程
- **Morgan (CEO)** 首先分析需求并制定分析计划
- **Leo** 检索最新的市场信息和数据
- **Ken** 深度挖掘题材逻辑和分支机会
- **Lus** 识别具体的个股投资机会
- **Jess** 汇总所有信息生成最终报告

#### 4️⃣ 查看分析结果
- 实时查看各代理的分析过程
- 获得结构化的投资分析报告
- 支持Markdown格式的富文本显示

### 🎯 咨询示例

#### 📊 题材分析类
```
• "请分析人工智能题材的投资逻辑"
• "新能源汽车产业链有哪些投资机会？"
• "医药生物板块的最新政策影响分析"
• "半导体国产化替代的投资机会"
```

#### 🔍 个股研究类
```
• "请分析比亚迪的投资价值"
• "宁德时代的竞争优势在哪里？"
• "茅台股价还能继续上涨吗？"
• "哪些芯片股值得长期持有？"
```

#### 📈 市场趋势类
```
• "当前A股市场的主要投资主线是什么？"
• "下半年哪些板块可能表现较好？"
• "美联储政策对A股的影响分析"
• "人民币汇率变化对出口企业的影响"
```

#### 🎲 策略咨询类
```
• "如何构建一个均衡的投资组合？"
• "熊市中应该采用什么投资策略？"
• "价值投资在A股市场是否有效？"
• "如何识别和规避投资风险？"
```

### 💡 使用技巧

1. **问题描述要具体**：越详细的问题，代理们能提供越精准的分析
2. **善用历史记录**：左侧历史面板可以快速回顾之前的分析
3. **关注过程细节**：每个代理的分析过程都包含有价值的信息
4. **及时保存重要内容**：可以复制重要的分析结果进行保存

## 🏗️ 项目架构

### 📁 目录结构

```
stock_agent_html/
├── 🚀 核心文件
│   ├── app.py                    # Flask Web应用主程序
│   ├── agent_v3.py              # 多代理系统核心逻辑
│   ├── run.py                   # 系统启动入口
│   └── requirements.txt         # Python依赖包列表
│
├── 🎨 前端资源
│   ├── templates/
│   │   ├── index.html          # 主页面模板
│   │   └── test_*.html         # 测试页面模板
│   └── static/
│       ├── css/
│       │   └── style.css       # 样式表文件
│       └── js/
│           └── app.js          # 前端交互逻辑
│
├── 🤖 代理配置
│   └── prompts/
│       ├── ceo.txt             # CEO代理提示词
│       ├── leo.txt             # 信息检索专家提示词
│       ├── ken.txt             # 题材挖掘专家提示词
│       ├── lus.txt             # A股炒作大师提示词
│       └── jess.txt            # 执行秘书提示词
│
├── 💾 数据存储
│   ├── messages/               # 会话消息存储目录
│   │   └── session_*.jsonl    # 各会话的消息记录
│   └── config/
│       ├── __init__.py
│       └── settings.py         # 系统配置文件
│
├── 🛠️ 工具脚本
│   ├── start_debug.bat         # Windows调试启动脚本
│   ├── toggle_debug_mode.py    # 调试模式切换工具
│   ├── clean_duplicate_messages.py  # 消息去重工具
│   └── test_*.py              # 各种测试脚本
│
└── 📚 工具库
    └── utils/
        ├── __init__.py
        └── logger.py           # 日志工具
```

### 🔧 技术架构

```mermaid
graph TB
    A[用户浏览器] -->|WebSocket| B[Flask-SocketIO]
    B --> C[Flask Web应用]
    C --> D[多进程代理系统]

    D --> E[Morgan CEO]
    D --> F[Leo 信息检索]
    D --> G[Ken 题材挖掘]
    D --> H[Lus A股分析]
    D --> I[Jess 秘书]

    E -->|任务分配| F
    E -->|任务分配| G
    G -->|协作| H
    F -->|信息提供| G
    H -->|结果汇总| I
    I -->|最终报告| C

    C -->|实时消息| B
    B -->|推送结果| A

    J[消息存储] -->|JSONL格式| C
    K[代理提示词] --> D
    L[外部API] -->|股票数据| F
```

## 🔧 技术栈详解

### 🖥️ 后端技术

| 技术 | 版本 | 用途 | 特点 |
|------|------|------|------|
| **Flask** | 2.3+ | Web框架 | 轻量级、灵活、易扩展 |
| **Flask-SocketIO** | 5.3+ | WebSocket支持 | 实时双向通信 |
| **AgentScope** | 0.0.5+ | 多代理框架 | 微软开源、支持复杂代理交互 |
| **Python-dotenv** | 1.0+ | 环境变量管理 | 安全的配置管理 |
| **Pydantic** | 2.0+ | 数据验证 | 类型安全、数据校验 |
| **Eventlet** | 0.33+ | 异步处理 | 高并发WebSocket支持 |

### 🎨 前端技术

| 技术 | 版本 | 用途 | 特点 |
|------|------|------|------|
| **HTML5** | - | 页面结构 | 语义化标签、现代化特性 |
| **CSS3** | - | 样式设计 | Flexbox、Grid、动画效果 |
| **JavaScript** | ES6+ | 交互逻辑 | 模块化、异步处理 |
| **Socket.IO** | 4.0+ | 实时通信 | 自动降级、连接管理 |
| **Markdown-it** | 13.0+ | 内容渲染 | 高性能Markdown解析 |
| **Font Awesome** | 6.0+ | 图标系统 | 丰富的图标库 |

### 🤖 AI模型配置

| 代理 | 模型 | 配置名称 | 特点 |
|------|------|----------|------|
| Morgan (CEO) | DeepSeek-V3 | 规划师 | 强大的推理和规划能力 |
| Ken (题材专家) | DeepSeek-R1 | 思考者 | 深度思考和逻辑分析 |
| Leo/Lus/Jess | Kimi-K2 | 工具使用者 | 优秀的工具调用能力 |

### 🔌 外部集成

- **MCP服务器**：股票RAG工具集成
- **实时数据源**：股票行情、新闻资讯
- **API接口**：支持多种LLM提供商

## 🆚 版本对比分析

### Gradio版本 vs HTML版本

| 维度 | Gradio版本 | HTML版本 | 改进说明 |
|------|------------|----------|----------|
| **界面美观度** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 完全自定义UI设计，现代化视觉效果 |
| **用户体验** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 流畅的交互动画，直观的操作流程 |
| **响应式设计** | ⭐⭐ | ⭐⭐⭐⭐⭐ | 完美适配各种设备尺寸 |
| **实时性能** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | WebSocket实时通信，零延迟体验 |
| **功能丰富度** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 会话管理、历史记录、搜索功能 |
| **部署灵活性** | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | 支持多种部署方式，易于扩展 |
| **开发复杂度** | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ | 模块化架构，代码结构清晰 |
| **维护成本** | ⭐⭐ | ⭐⭐⭐⭐ | 更好的错误处理和日志系统 |

### 🎯 核心优势

#### ✅ HTML版本的优势
- **🎨 完全自定义的UI/UX设计**
- **📱 原生响应式布局**
- **⚡ WebSocket实时通信**
- **💾 完整的会话管理系统**
- **🔍 智能搜索和过滤功能**
- **🛠️ 强大的调试和测试工具**
- **📊 详细的消息处理和展示**

#### ⚠️ 需要考虑的因素
- **开发时间**：需要更多的前端开发工作
- **技术栈**：需要掌握Flask + WebSocket + 前端技术
- **维护复杂度**：相比Gradio需要维护更多代码

## 🛠️ 开发指南

### 🚀 快速开发

#### 添加新功能的标准流程

1. **📋 需求分析**
   ```bash
   # 明确功能需求和技术方案
   # 确定涉及的组件和接口
   ```

2. **🔧 后端开发**
   ```python
   # 在 app.py 中添加新的路由
   @app.route('/api/new-feature', methods=['POST'])
   def new_feature():
       # 实现业务逻辑
       return jsonify({'status': 'success'})
   ```

3. **🎨 前端开发**
   ```html
   <!-- 在 templates/index.html 中添加UI元素 -->
   <div class="new-feature">
       <!-- 新功能的HTML结构 -->
   </div>
   ```

4. **💅 样式设计**
   ```css
   /* 在 static/css/style.css 中添加样式 */
   .new-feature {
       /* 新功能的样式定义 */
   }
   ```

5. **⚡ 交互逻辑**
   ```javascript
   // 在 static/js/app.js 中添加JavaScript代码
   function handleNewFeature() {
       // 新功能的交互逻辑
   }
   ```

### 🎨 样式自定义

#### CSS变量系统
```css
:root {
    /* 主题色彩 */
    --primary-color: #2563eb;
    --secondary-color: #64748b;
    --accent-color: #f59e0b;

    /* 响应式断点 */
    --mobile-breakpoint: 768px;
    --tablet-breakpoint: 1024px;

    /* 动画参数 */
    --transition-speed: 0.3s;
    --animation-easing: cubic-bezier(0.4, 0, 0.2, 1);
}
```

#### 响应式设计
```css
/* 移动端适配 */
@media (max-width: var(--mobile-breakpoint)) {
    .container {
        padding: 1rem;
    }
}

/* 平板适配 */
@media (max-width: var(--tablet-breakpoint)) {
    .sidebar {
        transform: translateX(-100%);
    }
}
```

### 🤖 代理系统扩展

#### 添加新代理

1. **创建提示词文件**
   ```bash
   # 在 prompts/ 目录下创建新的提示词文件
   touch prompts/new_agent.txt
   ```

2. **修改代理初始化代码**
   ```python
   # 在 agent_v3.py 的 init_agent() 函数中添加
   with open('prompts/new_agent.txt', 'r', encoding='utf-8') as f:
       new_agent_prompt = f.read().format(now=now)

   agent_new = DialogAgent(
       name="NewAgent",
       model_config_name="思考者",
       sys_prompt=new_agent_prompt,
   )
   ```

3. **更新代理映射**
   ```python
   # 在 agent_names 字典中添加新代理
   agent_names = {
       'Morgan': agent_ceo,
       'Leo': agent_leo,
       'Ken': agent_ken,
       'Lus': agent_lus,
       'Jess': agent_jess,
       'NewAgent': agent_new,  # 新增
   }
   ```

### 🔧 调试工具

#### 启用调试模式
```python
# 在 app.py 中设置
DEBUG_MODE = True
DEBUG_SESSION_ID = "your-test-session-id"
```

#### 使用测试工具
```bash
# 运行各种测试脚本
python test_debug_mode.py          # 调试模式测试
python test_message_features.py    # 消息功能测试
python test_simple_tool.py         # 工具调用测试
```

#### 消息清理工具
```bash
# 清理重复消息
python clean_duplicate_messages.py

# 切换调试模式
python toggle_debug_mode.py
```

## 🔒 安全与合规

### ⚠️ 重要声明

> **本系统仅供学习和研究使用，不构成任何投资建议**

#### 📋 使用须知
- **教育目的**：本项目仅用于技术学习和AI代理系统研究
- **投资风险**：股市投资有风险，任何投资决策请谨慎考虑
- **数据准确性**：系统分析结果仅供参考，不保证数据的准确性和时效性
- **法律合规**：请遵守当地的金融法规和投资相关法律

#### 🔐 安全配置

```env
# 生产环境必须修改的配置
SECRET_KEY=your-strong-secret-key-here    # 使用强密码
API_KEY=your-secure-api-key               # 保护好API密钥
DEBUG_MODE=false                          # 生产环境关闭调试
```

#### �️ 安全建议
- 定期更新依赖包版本
- 使用HTTPS部署生产环境
- 限制API访问频率
- 监控系统资源使用情况

## �📝 版本更新日志

### 🚀 v2.1.0 (计划中)
- [ ] 添加用户认证系统
- [ ] 支持自定义代理配置
- [ ] 增加数据导出功能
- [ ] 优化移动端体验

### ✅ v2.0.0 (当前版本) - 2024年
**重大架构升级**
- ✅ 完全重写前端界面，弃用Gradio
- ✅ 使用Flask + WebSocket实现实时通信
- ✅ 现代化的响应式UI设计
- ✅ 多会话管理和历史记录功能
- ✅ 优化的用户体验和交互流程
- ✅ 更好的错误处理和状态管理
- ✅ 支持Markdown渲染和工具调用展示
- ✅ 完整的调试和测试工具集

### 📚 v1.0.0 (Gradio版本) - 2023年
**基础功能实现**
- ✅ 基础的多代理系统架构
- ✅ Gradio界面实现
- ✅ 基本的股票题材分析功能
- ✅ 5个专业代理协同工作
- ✅ 简单的消息传递机制

## 🤝 参与贡献

我们欢迎所有形式的贡献！无论是代码、文档、测试还是建议。

### 🎯 贡献方式

#### 🐛 报告问题
- 使用GitHub Issues报告bug
- 提供详细的错误信息和复现步骤
- 包含系统环境和版本信息

#### 💡 功能建议
- 在Issues中提出新功能建议
- 详细描述功能需求和使用场景
- 讨论技术实现方案

#### 🔧 代码贡献
```bash
# 1. Fork项目到您的GitHub账户
# 2. 克隆您的Fork
git clone https://github.com/your-username/stock_agent_html.git

# 3. 创建功能分支
git checkout -b feature/your-feature-name

# 4. 提交更改
git commit -m "Add: your feature description"

# 5. 推送到您的Fork
git push origin feature/your-feature-name

# 6. 创建Pull Request
```

#### 📖 文档改进
- 改进README和代码注释
- 添加使用示例和教程
- 翻译文档到其他语言

### 🏆 贡献者

感谢所有为这个项目做出贡献的开发者！

## 📄 开源协议

本项目采用 **MIT License** 开源协议。

```
MIT License

Copyright (c) 2024 Super Stock Agents

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

<div align="center">

**🌟 如果这个项目对您有帮助，请给我们一个Star！**

[![GitHub stars](https://img.shields.io/github/stars/your-username/stock_agent_html.svg?style=social&label=Star)](https://github.com/your-username/stock_agent_html)
[![GitHub forks](https://img.shields.io/github/forks/your-username/stock_agent_html.svg?style=social&label=Fork)](https://github.com/your-username/stock_agent_html)

**📧 联系我们**

如有任何问题或建议，欢迎通过以下方式联系：
- 📧 Email: <EMAIL>
- 💬 GitHub Issues: [提交问题](https://github.com/your-username/stock_agent_html/issues)
- 🐦 Twitter: [@your-twitter](https://twitter.com/your-twitter)

</div>
